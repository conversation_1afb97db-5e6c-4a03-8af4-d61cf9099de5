import { BaseEntity } from "../base/base.types"

/**
 * Experience availability status
 */
export type ExperienceAvailabilityStatus = "available" | "unavailable" | "limited"

/**
 * Experience categories
 */
export type ExperienceCategory =
  | "default"
  | "adventure"
  | "food"
  | "culture"
  | "entertainment"
  | "outdoor"
  | "indoor"

/**
 * Host information embedded in experience
 */
export interface ExperienceHost {
  name: string
  avatar?: string
  responseTime: string // e.g., "Usually responds within an hour"
  languages: string[] // e.g., ["English", "Spanish"]
  bio: string
  email?: string // Host contact email (public-facing)
  phone?: string // Host contact phone number
  internalHostEmail?: string // Internal email for notifications and admin communication
}

/**
 * Experience location information
 */
export interface ExperienceLocation {
  address: string
  city: string
  state?: string
  country: string
  coordinates?: {
    lat: number
    lng: number
  }
  placeId?: string // Google Places ID
}

/**
 * What's included in the experience
 */
export interface ExperienceInclusion {
  item: string
  included: boolean
}

/**
 * Experience pricing information
 */
export interface ExperiencePricing {
  basePrice: number // Price per person
  currency: string // e.g., "USD"
  priceBreakdown?: {
    description: string
    amount: number
  }[]
}

/**
 * Experience availability slot
 */
export interface ExperienceAvailability {
  date: string // YYYY-MM-DD format or "default" for default availability
  timeSlots: {
    availabilityId: string // e.g., "slot-09-00", "slot-10-00"
    time: string // HH:MM format
    available: boolean
    maxGuests: number
    currentBookings: number
    remainingSpots?: number // Calculated field: maxGuests - currentBookings
  }[]
  isDefault?: boolean // true for default availability template
}

/**
 * Local Experience entity
 */
/**
 * Booking model for local experiences
 */
export type BookingModel = "per_session" | "per_max_guest"

export interface LocalExperience extends BaseEntity {
  title: string
  description: string
  shortDescription: string // For card display
  host: ExperienceHost
  location: ExperienceLocation
  pricing: ExperiencePricing
  duration: number // Duration in minutes
  maxGuests: number
  minGuests: number
  categories: ExperienceCategory[]
  images: string[] // Array of image URLs
  inclusions: ExperienceInclusion[]
  cancellationPolicy: string
  rating: number // Average rating
  reviewCount: number
  isActive: boolean // Whether the experience is currently offered
  stripeProductId?: string // For payment processing
  bookingModel?: BookingModel // How availability is calculated (defaults to per_max_guest)
}

/**
 * Experience creation data
 */
export type LocalExperienceCreateData = Omit<
  LocalExperience,
  "id" | "createdAt" | "rating" | "reviewCount"
>

/**
 * Experience update data
 */
export type LocalExperienceUpdateData = Partial<Omit<LocalExperience, "id" | "createdAt">>

/**
 * Experience search filters
 */
export interface ExperienceSearchFilters {
  searchTerm?: string
  location?: string
  categories?: ExperienceCategory[]
  priceRange?: {
    min: number
    max: number
  }
  date?: string // YYYY-MM-DD format
  guests?: number
  sortBy?: "price_low" | "price_high" | "rating" | "newest"
}

/**
 * Experience search result
 */
export interface ExperienceSearchResult {
  experiences: LocalExperience[]
  total: number
  hasMore: boolean
}
