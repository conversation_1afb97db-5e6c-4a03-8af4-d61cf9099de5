import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * User entity
 */
export interface User extends BaseEntity {
  uid: string
  email: string
  name?: string // Legacy field for backward compatibility
  displayName: string
  photoURL?: string | null
  profilePictureUrl?: string // Legacy field for backward compatibility
  bio?: string
  location?: string
  locationPlaceId?: string
  referralCode?: string
  isAdmin?: boolean
  newUser?: boolean
  firstLogin?: Timestamp
}

/**
 * User creation data
 */
export type UserCreateData = Omit<User, "id" | "createdAt">

/**
 * User update data
 */
export type UserUpdateData = Partial<User>
