import { Timestamp } from "firebase-admin/firestore"

/**
 * Serializes Firestore data for passing to client components
 * Converts Timestamp objects to ISO strings and handles nested objects
 */
export function serializeFirestoreData<T = any>(data: any): T {
  if (data === null || data === undefined) {
    return data
  }

  // Check for Firestore Timestamp objects (both client and admin SDK)
  if (data && typeof data === 'object' &&
      typeof data._seconds === 'number' &&
      typeof data._nanoseconds === 'number') {
    // Convert Firestore Timestamp to ISO string
    const seconds = data._seconds
    const nanoseconds = data._nanoseconds
    const date = new Date(seconds * 1000 + nanoseconds / 1000000)
    return date.toISOString() as T
  }

  if (data instanceof Timestamp) {
    return data.toDate().toISOString() as T
  }

  if (Array.isArray(data)) {
    return data.map(item => serializeFirestoreData(item)) as T
  }

  if (typeof data === 'object' && data.constructor === Object) {
    const serialized: any = {}
    for (const [key, value] of Object.entries(data)) {
      serialized[key] = serializeFirestoreData(value)
    }
    return serialized as T
  }

  return data as T
}

/**
 * Serializes a Firestore document with its data
 */
export function serializeFirestoreDoc<T = any>(doc: any): T | null {
  if (!doc.exists) {
    return null
  }

  const data = doc.data()
  if (!data) {
    return null
  }

  return serializeFirestoreData({
    ...data,
    id: doc.id,
  }) as T
}

/**
 * Serializes an array of Firestore documents
 */
export function serializeFirestoreDocs<T = any>(docs: any[]): T[] {
  return docs
    .map(doc => serializeFirestoreDoc<T>(doc))
    .filter((item): item is T => item !== null)
}
