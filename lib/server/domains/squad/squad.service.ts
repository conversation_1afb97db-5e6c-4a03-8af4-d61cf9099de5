import { getFirestore } from "firebase-admin/firestore"
import { Squad, UserSquad } from "@/lib/domains/squad/squad.types"
import { serializeFirestoreData } from "@/lib/server/utils/firestore-serializer"

/**
 * Server-side Squad Service using Firebase Admin SDK
 */
export class SquadServerService {
  private static readonly COLLECTION = "squads"
  private static readonly USER_SQUADS_SUBCOLLECTION = "squads"

  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async getSquad(squadId: string): Promise<Squad | null> {
    try {
      const adminDb = getFirestore()
      const squadDoc = await adminDb.collection(this.COLLECTION).doc(squadId).get()

      if (!squadDoc.exists) {
        return null
      }

      const data = squadDoc.data()
      if (!data) {
        return null
      }

      return serializeFirestoreData({
        ...data,
        id: squadId,
      }) as Squad
    } catch (error) {
      console.error("Error getting squad (server):", error)
      throw error
    }
  }

  /**
   * Get multiple squads by IDs
   * @param squadIds Array of squad IDs
   * @returns Array of squads
   */
  static async getSquads(squadIds: string[]): Promise<Squad[]> {
    if (squadIds.length === 0) {
      return []
    }

    try {
      const adminDb = getFirestore()
      const squads: Squad[] = []

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      const batchSize = 10
      for (let i = 0; i < squadIds.length; i += batchSize) {
        const batch = squadIds.slice(i, i + batchSize)
        const querySnapshot = await adminDb
          .collection(this.COLLECTION)
          .where(
            "__name__",
            "in",
            batch.map((id) => adminDb.collection(this.COLLECTION).doc(id))
          )
          .get()

        for (const doc of querySnapshot.docs) {
          const data = doc.data()
          squads.push(
            serializeFirestoreData({
              ...data,
              id: doc.id,
            }) as Squad
          )
        }
      }

      return squads
    } catch (error) {
      console.error("Error getting squads (server):", error)
      throw error
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads the user belongs to
   */
  static async getUserSquads(userId: string): Promise<Squad[]> {
    try {
      const adminDb = getFirestore()

      // Get user's squad subcollection
      const userSquadsSnapshot = await adminDb
        .collection("users")
        .doc(userId)
        .collection(this.USER_SQUADS_SUBCOLLECTION)
        .get()

      if (userSquadsSnapshot.empty) {
        return []
      }

      // Extract squad IDs
      const squadIds = userSquadsSnapshot.docs.map((doc) => doc.data().squadId).filter(Boolean)

      if (squadIds.length === 0) {
        return []
      }

      // Get squad details
      return await this.getSquads(squadIds)
    } catch (error) {
      console.error("Error getting user squads (server):", error)
      throw error
    }
  }

  /**
   * Get squad members
   * @param squadId Squad ID
   * @returns Array of squad members
   */
  static async getSquadMembers(squadId: string): Promise<any[]> {
    try {
      const adminDb = getFirestore()
      const membersSnapshot = await adminDb
        .collection(this.COLLECTION)
        .doc(squadId)
        .collection("members")
        .get()

      return membersSnapshot.docs.map((doc) =>
        serializeFirestoreData({
          ...doc.data(),
          id: doc.id,
        })
      )
    } catch (error) {
      console.error("Error getting squad members (server):", error)
      throw error
    }
  }
}
