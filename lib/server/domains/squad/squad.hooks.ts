import { Squad } from "@/lib/domains/squad/squad.types"
import { SquadServerService } from "./squad.service"

/**
 * Server-side Squad Hooks using Firebase Admin SDK
 * These hooks are designed for server-side operations and require userId parameter
 * since they cannot access client-side auth stores
 */
export class SquadServerHooks {
  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async useSquad(squadId: string): Promise<Squad | null> {
    try {
      return await SquadServerService.getSquad(squadId)
    } catch (error) {
      console.error("Error in useSquad (server):", error)
      return null
    }
  }

  /**
   * Get multiple squads by IDs
   * @param squadIds Array of squad IDs
   * @returns Array of squads
   */
  static async useSquadsFromIds(squadIds: string[]): Promise<Squad[]> {
    try {
      return await SquadServerService.getSquads(squadIds)
    } catch (error) {
      console.error("Error in useSquadsFromIds (server):", error)
      return []
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads the user belongs to
   */
  static async useUserSquads(userId: string): Promise<Squad[]> {
    try {
      return await SquadServerService.getUserSquads(userId)
    } catch (error) {
      console.error("Error in useUserSquads (server):", error)
      return []
    }
  }

  /**
   * Get squad members
   * @param squadId Squad ID
   * @returns Array of squad members
   */
  static async useSquadMembers(squadId: string): Promise<any[]> {
    try {
      return await SquadServerService.getSquadMembers(squadId)
    } catch (error) {
      console.error("Error in useSquadMembers (server):", error)
      return []
    }
  }
}
