/**
 * Server-side Squad Domain Exports
 *
 * This module provides server-side squad functionality using Firebase Admin SDK.
 * All functions require explicit userId parameters since they cannot access
 * client-side auth stores.
 */

export { SquadServerService } from "./squad.service"
export { SquadServerHooks } from "./squad.hooks"

// Re-export types from the client-side domain for convenience
export type {
  Squad,
  SquadMember,
  UserSquad,
  SquadCreateData,
  SquadUpdateData,
  SquadFormData,
  SquadRole,
  SquadStatus,
} from "@/lib/domains/squad/squad.types"
