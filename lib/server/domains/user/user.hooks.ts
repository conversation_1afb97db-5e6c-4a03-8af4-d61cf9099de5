import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "./user.service"

/**
 * Server-side User Hooks using Firebase Admin SDK
 * These hooks are designed for server-side operations
 */
export class UserServerHooks {
  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async useUser(userId: string): Promise<User | null> {
    try {
      return await UserServerService.getUser(userId)
    } catch (error) {
      console.error("Error in useUser (server):", error)
      return null
    }
  }

  /**
   * Get multiple users by IDs
   * @param userIds Array of user IDs
   * @returns Array of users
   */
  static async useUsersFromIds(userIds: string[]): Promise<User[]> {
    try {
      return await UserServerService.getUsers(userIds)
    } catch (error) {
      console.error("Error in useUsersFromIds (server):", error)
      return []
    }
  }

  /**
   * Check if user is admin
   * @param userId User ID
   * @returns True if user is admin, false otherwise
   */
  static async useIsUserAdmin(userId: string): Promise<boolean> {
    try {
      return await UserServerService.isUserAdmin(userId)
    } catch (error) {
      console.error("Error in useIsUserAdmin (server):", error)
      return false
    }
  }

  /**
   * Check if user is new
   * @param userId User ID
   * @returns True if user is new, false otherwise, null if user not found
   */
  static async useIsNewUser(userId: string): Promise<boolean | null> {
    try {
      return await UserServerService.isNewUser(userId)
    } catch (error) {
      console.error("Error in useIsNewUser (server):", error)
      return null
    }
  }
}
