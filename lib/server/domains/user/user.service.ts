import { getFirestore } from "firebase-admin/firestore"
import { User } from "@/lib/domains/user/user.types"
import { serializeFirestoreData } from "@/lib/server/utils/firestore-serializer"

/**
 * Server-side User Service using Firebase Admin SDK
 */
export class UserServerService {
  private static readonly COLLECTION = "users"

  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async getUser(userId: string): Promise<User | null> {
    try {
      const adminDb = getFirestore()
      const userDoc = await adminDb.collection(this.COLLECTION).doc(userId).get()

      if (!userDoc.exists) {
        return null
      }

      const data = userDoc.data()
      if (!data) {
        return null
      }

      return serializeFirestoreData({
        ...data,
        id: userId,
        uid: userId, // Ensure uid is set for compatibility
      }) as User
    } catch (error) {
      console.error("Error getting user (server):", error)
      throw error
    }
  }

  /**
   * Get multiple users by IDs
   * @param userIds Array of user IDs
   * @returns Array of users
   */
  static async getUsers(userIds: string[]): Promise<User[]> {
    if (userIds.length === 0) {
      return []
    }

    try {
      const adminDb = getFirestore()
      const users: User[] = []

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      const batchSize = 10
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize)
        const querySnapshot = await adminDb
          .collection(this.COLLECTION)
          .where(
            "__name__",
            "in",
            batch.map((id) => adminDb.collection(this.COLLECTION).doc(id))
          )
          .get()

        for (const doc of querySnapshot.docs) {
          const data = doc.data()
          users.push(
            serializeFirestoreData({
              ...data,
              id: doc.id,
              uid: doc.id, // Ensure uid is set for compatibility
            }) as User
          )
        }
      }

      return users
    } catch (error) {
      console.error("Error getting users (server):", error)
      throw error
    }
  }

  /**
   * Check if user is admin
   * @param userId User ID
   * @returns True if user is admin, false otherwise
   */
  static async isUserAdmin(userId: string): Promise<boolean> {
    try {
      const user = await this.getUser(userId)
      return user?.isAdmin === true
    } catch (error) {
      console.error("Error checking admin status (server):", error)
      return false
    }
  }

  /**
   * Check if user is new (has newUser flag)
   * @param userId User ID
   * @returns True if user is new, false otherwise, null if user not found
   */
  static async isNewUser(userId: string): Promise<boolean | null> {
    try {
      const user = await this.getUser(userId)
      if (!user) return null
      return user.newUser === true
    } catch (error) {
      console.error("Error checking new user status (server):", error)
      return null
    }
  }
}
