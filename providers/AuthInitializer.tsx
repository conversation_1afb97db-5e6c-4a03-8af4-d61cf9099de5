"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { AuthRealtimeService } from "@/lib/domains/auth/auth.realtime.service"

export default function AuthInitializer() {
  const { setUser, setLoading } = useAuthStore()

  useEffect(() => {
    setLoading(true)

    // Subscribe to auth state changes
    const unsubscribeAuth = AuthRealtimeService.subscribeToAuthState((user, error) => {
      if (error) console.error(error)
      setUser(user)
      setLoading(false)
    })

    return () => {
      unsubscribeAuth()
    }
  }, [setUser, setLoading])

  return null
}
