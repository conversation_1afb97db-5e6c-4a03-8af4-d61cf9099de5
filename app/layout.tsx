import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Providers } from "@/components/providers"
import AuthInitializer from "@/providers/AuthInitializer"
import { PerformanceMonitor } from "@/components/performance-monitor"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Togeda.ai - Plan Your Next Adventure",
  description: "Plan and organize trips with your friends",
  icons: {
    icon: [
      { url: "/togeda_favicon.svg", type: "image/svg+xml" },
      { url: "/togeda_favicon.ico", type: "image/x-icon" },
    ],
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    title: "Togeda.ai - Plan Your Next Adventure",
    statusBarStyle: "default",
  },
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Resource hints for third-party origins to reduce connection time */}
        <link rel="preconnect" href="https://apis.google.com" />
        <link rel="preconnect" href="https://www.googleapis.com" />
        <link rel="preconnect" href="https://identitytoolkit.googleapis.com" />
        <link rel="preconnect" href="https://brotrip-mvp.firebaseapp.com" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="dns-prefetch" href="//firebaseapp.com" />
        <link rel="dns-prefetch" href="//googleapis.com" />
        <link rel="dns-prefetch" href="//www.gstatic.com" />
      </head>
      <body className={inter.className}>
        <PerformanceMonitor />
        <AuthInitializer />
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
