import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { Timestamp } from "firebase-admin/firestore"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Boolean)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Validate cron request authorization and origin
 */
function validateCronRequest(request: NextRequest) {
  // Check authorization header
  const authHeader = request.headers.get("authorization")
  const expectedAuth = `Bearer ${CRON_SECRET}`

  if (!authHeader || authHeader !== expectedAuth) {
    return { isValid: false, error: "Unauthorized" }
  }

  // Check origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")

  // Allow requests from Vercel cron or direct server calls
  if (origin && !ALLOWED_ORIGINS.includes(origin)) {
    // Allow if it's a Vercel cron job (no origin but specific user agent pattern)
    if (!userAgent?.includes("vercel")) {
      return { isValid: false, error: "Forbidden origin" }
    }
  }

  return { isValid: true }
}

/**
 * Calculate the time difference in hours between two dates
 */
function getHoursDifference(date1: Date, date2: Date): number {
  return Math.abs(date2.getTime() - date1.getTime()) / (1000 * 60 * 60)
}

/**
 * Check if a booking needs a specific reminder notification
 */
function shouldSendReminder(
  experienceDateTime: Date,
  currentTime: Date,
  reminderType: "7d" | "3d" | "1d" | "2h",
  emailNotifications?: { [key: string]: boolean }
): boolean {
  const hoursUntilExperience =
    (experienceDateTime.getTime() - currentTime.getTime()) / (1000 * 60 * 60)

  // Don't send if already sent
  if (emailNotifications?.[reminderType]) {
    return false
  }

  // Check if we're in the right time window for each reminder
  switch (reminderType) {
    case "7d":
      // Send 7 days before (between 7 days and 6 days 12 hours)
      return hoursUntilExperience <= 168 && hoursUntilExperience > 156
    case "3d":
      // Send 3 days before (between 3 days and 2 days 12 hours)
      return hoursUntilExperience <= 72 && hoursUntilExperience > 60
    case "1d":
      // Send 1 day before (between 1 day and 12 hours)
      return hoursUntilExperience <= 24 && hoursUntilExperience > 12
    case "2h":
      // Send 2 hours before (between 2 hours and 1 hour)
      return hoursUntilExperience <= 2 && hoursUntilExperience > 1
    default:
      return false
  }
}

/**
 * Process experience reminder notifications
 */
async function processExperienceReminders() {
  const startTime = Date.now()
  const currentTime = new Date()

  console.log(`🔔 Starting experience reminder processing at ${currentTime.toISOString()}`)

  const { adminDb } = await getAdminInstance()

  // Get all confirmed bookings that haven't been completed yet
  // Using single filter to avoid composite index requirement
  const bookingsQuery = adminDb.collectionGroup("bookings").where("status", "==", "confirmed")

  const bookingsSnapshot = await bookingsQuery.get()

  // Filter for paid bookings in memory to avoid composite index
  const confirmedPaidBookings = bookingsSnapshot.docs.filter((doc) => {
    const data = doc.data()
    return data.paymentStatus === "paid"
  })

  console.log(
    `Found ${confirmedPaidBookings.length} confirmed and paid bookings to check (filtered from ${bookingsSnapshot.size} confirmed bookings)`
  )

  const remindersToSend: Array<{
    bookingId: string
    experienceId: string
    reminderType: "7d" | "3d" | "1d" | "2h"
    bookingData: any
  }> = []

  const batch = adminDb.batch()
  let batchCount = 0

  // Process each booking
  for (const bookingDoc of confirmedPaidBookings) {
    try {
      const bookingData = bookingDoc.data()
      const bookingId = bookingDoc.id

      // Extract experience ID from the document path
      const pathParts = bookingDoc.ref.path.split("/")
      const experienceId = pathParts[1] // localExperiences/{experienceId}/bookings/{bookingId}

      // Parse the experience date and time
      const experienceDate = bookingData.date // YYYY-MM-DD format
      const experienceTime = bookingData.time // HH:MM format

      // Create full datetime in UTC
      const experienceDateTime = new Date(`${experienceDate}T${experienceTime}:00.000Z`)

      // Skip if experience is in the past
      if (experienceDateTime <= currentTime) {
        continue
      }

      const emailNotifications = bookingData.emailNotifications || {}

      // Check each reminder type
      const reminderTypes: Array<"7d" | "3d" | "1d" | "2h"> = ["7d", "3d", "1d", "2h"]

      for (const reminderType of reminderTypes) {
        if (shouldSendReminder(experienceDateTime, currentTime, reminderType, emailNotifications)) {
          console.log(`Scheduling ${reminderType} reminder for booking ${bookingId}`)

          // Update the emailNotifications field
          const updatedNotifications = {
            ...emailNotifications,
            [reminderType]: true,
          }

          // Add to batch update
          batch.update(bookingDoc.ref, {
            emailNotifications: updatedNotifications,
            updatedAt: Timestamp.now(),
          })

          batchCount++

          // Track for reporting
          remindersToSend.push({
            bookingId,
            experienceId,
            reminderType,
            bookingData,
          })

          // Commit batch if it gets too large
          if (batchCount >= 500) {
            await batch.commit()
            console.log(`Committed batch of ${batchCount} updates`)
            batchCount = 0
          }
        }
      }
    } catch (error) {
      console.error(`Error processing booking ${bookingDoc.id}:`, error)
    }
  }

  // Commit remaining updates
  if (batchCount > 0) {
    await batch.commit()
    console.log(`Committed final batch of ${batchCount} updates`)
  }

  const duration = Date.now() - startTime

  return {
    bookingsProcessed: confirmedPaidBookings.length,
    remindersScheduled: remindersToSend.length,
    reminderBreakdown: {
      "7d": remindersToSend.filter((r) => r.reminderType === "7d").length,
      "3d": remindersToSend.filter((r) => r.reminderType === "3d").length,
      "1d": remindersToSend.filter((r) => r.reminderType === "1d").length,
      "2h": remindersToSend.filter((r) => r.reminderType === "2h").length,
    },
    duration,
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === "Unauthorized" ? 401 : 403 }
      )
    }

    const result = await processExperienceReminders()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Experience reminder processing completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in experience reminder cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
