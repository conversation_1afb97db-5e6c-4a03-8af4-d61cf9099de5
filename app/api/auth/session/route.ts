import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { cookies } from "next/headers"

export interface CreateSessionRequest {
  idToken: string
}

export interface CreateSessionResponse {
  success: boolean
  error?: string
}

/**
 * Create a session cookie from Firebase ID token
 * This enables server-side authentication for SSR
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { idToken }: CreateSessionRequest = await request.json()

    if (!idToken) {
      return NextResponse.json({ success: false, error: "ID token is required" }, { status: 400 })
    }

    // Initialize Firebase Admin
    const { adminAuth } = await getAdminInstance()

    if (!adminAuth) {
      console.error("Firebase Admin Auth is not initialized")
      return NextResponse.json(
        { success: false, error: "Server authentication not available" },
        { status: 500 }
      )
    }

    // Verify the ID token first
    const decodedToken = await adminAuth.verifyIdToken(idToken)

    if (!decodedToken) {
      return NextResponse.json({ success: false, error: "Invalid ID token" }, { status: 401 })
    }

    // Create session cookie (5 days expiration)
    const expiresIn = 60 * 60 * 24 * 5 * 1000 // 5 days in milliseconds
    const sessionCookie = await adminAuth.createSessionCookie(idToken, { expiresIn })

    // Set the session cookie
    const cookieStore = await cookies()
    cookieStore.set("session", sessionCookie, {
      maxAge: Math.floor(expiresIn / 1000), // Convert to seconds for cookie maxAge
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error creating session cookie:", error)
    return NextResponse.json({ success: false, error: "Failed to create session" }, { status: 500 })
  }
}

/**
 * Delete the session cookie (logout)
 */
export async function DELETE(): Promise<NextResponse> {
  try {
    const cookieStore = await cookies()

    // Clear the session cookie
    cookieStore.set("session", "", {
      maxAge: 0,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting session cookie:", error)
    return NextResponse.json({ success: false, error: "Failed to delete session" }, { status: 500 })
  }
}
