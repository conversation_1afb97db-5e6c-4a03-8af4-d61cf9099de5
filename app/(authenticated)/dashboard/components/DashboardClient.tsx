"use client"

import { useEffect, Suspense, lazy } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { useUserStore } from "@/lib/domains/user/user.store"
import { useSquadStore } from "@/lib/domains/squad/squad.store"
import { useTripStore } from "@/lib/domains/trip/trip.store"
import type { ServerUser } from "@/lib/server/auth"
import type { Squad } from "@/lib/domains/squad/squad.types"
import type { Trip } from "@/lib/domains/trip/trip.types"
import type { User } from "@/lib/domains/user/user.types"

// Lazy load tab components to reduce initial bundle size
const SquadsTab = lazy(() =>
  import("./SquadsTab").then((module) => ({ default: module.SquadsTab }))
)
const UpcomingTripsTab = lazy(() =>
  import("./UpcomingTripsTab").then((module) => ({ default: module.UpcomingTripsTab }))
)
const PastTripsTab = lazy(() =>
  import("./PastTripsTab").then((module) => ({ default: module.PastTripsTab }))
)

// Tab loading skeleton component
const TabSkeleton = () => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted animate-pulse rounded" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  </div>
)

interface DashboardClientProps {
  serverUser: ServerUser
  initialUser: User | null
  initialSquads: Squad[]
  initialUpcomingTrips: Trip[]
  initialPastTrips: Trip[]
}

export function DashboardClient({
  serverUser,
  initialUser,
  initialSquads,
  initialUpcomingTrips,
  initialPastTrips,
}: DashboardClientProps) {
  // Initialize stores with server data
  useEffect(() => {
    // Initialize user store with server data
    if (initialUser) {
      useUserStore.setState({ user: initialUser })
    }

    // Initialize squad store with server data
    useSquadStore.setState({ squads: initialSquads })

    // Initialize trip store with server data
    const allInitialTrips = [...initialUpcomingTrips, ...initialPastTrips]
    useTripStore.setState({ trips: allInitialTrips })
  }, [initialUser, initialSquads, initialUpcomingTrips, initialPastTrips])

  // Get current user from store (will be initialized with server data)
  const user = useUserStore((state) => state.user)

  // Get real-time updates (these will start with server data and then update)
  const { squads, loading: squadsLoading } = useRealtimeUserSquads(initialSquads)
  const { upcomingTrips, pastTrips, loading: tripsLoading } = useRealtimeUserAllTrips(
    initialUpcomingTrips,
    initialPastTrips
  )

  // Use server data immediately, no loading state needed for initial render
  const displayUser = user || initialUser
  const displaySquads = squads.length > 0 ? squads : initialSquads
  const displayUpcomingTrips = upcomingTrips.length > 0 ? upcomingTrips : initialUpcomingTrips
  const displayPastTrips = pastTrips.length > 0 ? pastTrips : initialPastTrips

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back, {displayUser?.displayName || displayUser?.name || serverUser.displayName || "Friend"}!
        </p>
      </div>

      <Tabs defaultValue="trips" className="space-y-4">
        <TabsList className="w-full">
          <TabsTrigger value="trips" className="flex-1 md:flex-initial">
            Upcoming Trips
          </TabsTrigger>
          <TabsTrigger value="squads" className="flex-1 md:flex-initial">
            My Squads
          </TabsTrigger>
          <TabsTrigger value="past" className="flex-1 md:flex-initial">
            Past Trips
          </TabsTrigger>
        </TabsList>

        <TabsContent value="squads">
          <Suspense fallback={<TabSkeleton />}>
            <SquadsTab 
              squads={displaySquads} 
              upcomingTrips={displayUpcomingTrips} 
              loading={squadsLoading && displaySquads.length === 0}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="trips">
          <Suspense fallback={<TabSkeleton />}>
            <UpcomingTripsTab 
              squads={displaySquads} 
              upcomingTrips={displayUpcomingTrips} 
              loading={tripsLoading && displayUpcomingTrips.length === 0}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="past">
          <Suspense fallback={<TabSkeleton />}>
            <PastTripsTab 
              squads={displaySquads} 
              pastTrips={displayPastTrips} 
              loading={tripsLoading && displayPastTrips.length === 0}
            />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}
