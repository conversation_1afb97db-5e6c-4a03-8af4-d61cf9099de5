"use client"

import { useState, useEffect, Suspense, lazy } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ExperienceDetailModal } from "./components/experience-detail-modal"
import { PageLoading } from "@/components/page-loading"

// Lazy load tab components to reduce initial bundle size
const DiscoveryTab = lazy(() =>
  import("./components/discovery-tab").then((module) => ({ default: module.DiscoveryTab }))
)
const UserBookingsTab = lazy(() =>
  import("./components/user-bookings-tab").then((module) => ({ default: module.UserBookingsTab }))
)

// Tab loading skeleton component
const ExperienceTabSkeleton = () => (
  <div className="space-y-6">
    {/* Search/Filter Section Skeleton */}
    <div className="space-y-4">
      <div className="h-10 w-full bg-muted animate-pulse rounded-lg" />
      <div className="flex gap-2">
        <div className="h-8 w-20 bg-muted animate-pulse rounded" />
        <div className="h-8 w-24 bg-muted animate-pulse rounded" />
        <div className="h-8 w-28 bg-muted animate-pulse rounded" />
      </div>
    </div>

    {/* Content Grid Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="space-y-3">
          <div className="h-48 bg-muted animate-pulse rounded-lg" />
          <div className="space-y-2">
            <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
            <div className="h-3 w-1/2 bg-muted animate-pulse rounded" />
            <div className="h-6 w-20 bg-muted animate-pulse rounded" />
          </div>
        </div>
      ))}
    </div>
  </div>
)

function ExperiencesPageContent() {
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("discover")

  // Handle tab parameter from URL
  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (tabParam === "my-bookings") {
      setActiveTab("bookings")
    }
  }, [searchParams])

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Local Experiences</h1>
            <p className="text-muted-foreground mt-2">
              Discover unique activities and manage your bookings
            </p>
          </div>
        </div>
      </div>

      {/* Main Content with Tabs */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 max-w-md">
            <TabsTrigger value="discover">Discover</TabsTrigger>
            <TabsTrigger value="bookings">My Bookings</TabsTrigger>
          </TabsList>

          <TabsContent value="discover" className="space-y-0">
            <Suspense fallback={<ExperienceTabSkeleton />}>
              <DiscoveryTab />
            </Suspense>
          </TabsContent>

          <TabsContent value="bookings" className="space-y-0">
            <Suspense fallback={<ExperienceTabSkeleton />}>
              <UserBookingsTab />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <ExperienceDetailModal />
    </div>
  )
}

export default function ExperiencesPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading experiences..." />}>
      <ExperiencesPageContent />
    </Suspense>
  )
}
